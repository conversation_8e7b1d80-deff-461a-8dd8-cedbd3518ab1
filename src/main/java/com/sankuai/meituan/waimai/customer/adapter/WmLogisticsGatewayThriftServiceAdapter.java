package com.sankuai.meituan.waimai.customer.adapter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.waimai.customer.config.MccConfig;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.exception.HeronContractGatewayThriftException;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.param.flow.sign.*;
import com.sankuai.meituan.waimai.heron.contract.gateway.thrift.client.service.HeronContractFlowProcessGatewayThriftService;
import com.sankuai.meituan.waimai.thrift.customer.constant.CustomerErrorCodeConstants;
import com.sankuai.meituan.waimai.thrift.exception.WmCustomerException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 配送新合同服务
 *
 * Created by lixuepeng on 2023/5/31
 */
@Service
public class WmLogisticsGatewayThriftServiceAdapter {

    // 线程池

    private static final ExecutorService DELIVERY_MANUAL_PACK_APPLY_EXECUTOR = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(16,
                    32,
                    60L,
                    TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(1024),
                    new ThreadFactoryBuilder().setNameFormat("delivery_manual-pack-apply-thread-%d").build(),
                    new ThreadPoolExecutor.DiscardPolicy())
    );

    private static Logger log = LoggerFactory.getLogger(WmLogisticsGatewayThriftServiceAdapter.class);

    /**
     * 批量执行结果
     */
    public static class BatchExecutionResult {
        private final int totalTasks;
        private final int successCount;
        private final int failureCount;
        private final List<String> failureDetails;
        private final boolean timeoutOccurred;
        private final long executionTimeMs;

        public BatchExecutionResult(int totalTasks, int successCount, int failureCount, 
                                  List<String> failureDetails, boolean timeoutOccurred, long executionTimeMs) {
            this.totalTasks = totalTasks;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.failureDetails = failureDetails;
            this.timeoutOccurred = timeoutOccurred;
            this.executionTimeMs = executionTimeMs;
        }

        public int getTotalTasks() { return totalTasks; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public List<String> getFailureDetails() { return failureDetails; }
        public boolean isTimeoutOccurred() { return timeoutOccurred; }
        public long getExecutionTimeMs() { return executionTimeMs; }

        public boolean isAllSuccess() { return failureCount == 0 && !timeoutOccurred; }
        public boolean hasFailures() { return failureCount > 0; }

        @Override
        public String toString() {
            return String.format("BatchExecutionResult{totalTasks=%d, successCount=%d, failureCount=%d, " +
                               "timeoutOccurred=%s, executionTimeMs=%d, failureDetails=%s}", 
                totalTasks, successCount, failureCount, timeoutOccurred, executionTimeMs, failureDetails);
        }
    }

    @Autowired
    private HeronContractFlowProcessGatewayThriftService heronContractFlowProcessGatewayThriftService;

    /**
     * 是否使用新的签约相关能力（发起签约、取消签约、取消待签约）
     * @param param
     * @return
     * @throws WmCustomerException
     */
    public boolean isDeliverySignOperateUseNewIface(HeronContractSignGrayParam param) throws WmCustomerException {
        try {
            log.info("#getGatewaySignOperateGrayResult param:{}", JSON.toJSONString(param));
            boolean result = heronContractFlowProcessGatewayThriftService.getGatewaySignOperateGrayResult(param);
            log.info("#getGatewaySignOperateGrayResult result:{}", result);
            return result;
        } catch (HeronContractGatewayThriftException e) {
            log.error("#isDeliveryApplySignUseNewIface 判断配送合同批量打包签约是否使用售卖网关接口业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#isDeliveryApplySignUseNewIface 判断配送合同批量打包签约是否使用售卖网关接口系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "判断配送合同批量打包签约是否使用售卖网关接口系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#isDeliverySignOperateUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "判断配送合同批量打包签约是否使用售卖网关接口系统异常");
        }
    }

    /**
     * 通过售卖网关发起打包签约
     * @param param 批量签约参数
     * @return 批量执行结果
     * @throws WmCustomerException
     */
    public BatchExecutionResult deliveryBatchApplySignUseNewIface(HeronContractManualBatchSignParam param) throws WmCustomerException {
        long startTime = System.currentTimeMillis();
        List<HeronContractManualSignItem> signItemList = param.getSignItemList();
        List<List<HeronContractManualSignItem>> partition = Lists.partition(signItemList, MccConfig.getManualPackGroupSize());
        
        log.info("开始批量签约，总任务数: {}, 分批数: {}", signItemList.size(), partition.size());
        
        try {
            // 提交所有任务
            List<Future<?>> futures = submitTasks(param, partition);
            
            // 等待任务完成
            boolean timeoutOccurred = waitForCompletion(futures, partition.size());
            
            // 收集执行结果
            BatchExecutionResult result = collectResults(futures, partition.size(), timeoutOccurred, startTime);
            
            log.info("批量签约完成，执行结果: {}", result);
            return result;
            
        } catch (InterruptedException e) {
            log.error("批量签约被中断", e);
            Thread.currentThread().interrupt();
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "批量签约任务被中断");
        }
    }

    /**
     * 提交所有任务到线程池
     */
    private List<Future<?>> submitTasks(HeronContractManualBatchSignParam param, 
                                      List<List<HeronContractManualSignItem>> partition) {
        List<Future<?>> futures = new ArrayList<>();
        
        for (int i = 0; i < partition.size(); i++) {
            List<HeronContractManualSignItem> signItems = partition.get(i);
            HeronContractManualBatchSignParam batchSignParam = createBatchSignParam(param, signItems);
            
            final int batchIndex = i;
            Future<?> future = DELIVERY_MANUAL_PACK_APPLY_EXECUTOR.submit(() -> {
                try {
                    log.debug("开始执行批次 {} 签约，包含 {} 个签约项", batchIndex, signItems.size());
                    applyBatchDeliverySign(batchSignParam);
                    log.info("批次 {} 签约成功，包含 {} 个签约项", batchIndex, signItems.size());
                } catch (Exception e) {
                    handleTaskException(batchIndex, signItems, e);
                }
            });
            futures.add(future);
        }
        
        log.info("已提交 {} 个批次任务到线程池", partition.size());
        return futures;
    }

    /**
     * 创建批次签约参数
     */
    private HeronContractManualBatchSignParam createBatchSignParam(HeronContractManualBatchSignParam param, 
                                                                 List<HeronContractManualSignItem> signItems) {
        HeronContractManualBatchSignParam batchSignParam = new HeronContractManualBatchSignParam();
        batchSignParam.setSignItemList(signItems);
        batchSignParam.setBatchManualConfirmId(param.getBatchManualConfirmId());
        batchSignParam.setOperator(param.getOperator());
        batchSignParam.setApplyType(param.getApplyType());
        return batchSignParam;
    }

    /**
     * 处理单个任务的异常
     */
    private void handleTaskException(int batchIndex, List<HeronContractManualSignItem> signItems, Exception e) {
        String errorMsg = String.format("批次 %d 签约失败，包含 %d 个签约项，错误: %s", 
            batchIndex, signItems.size(), e.getMessage());
        log.error(errorMsg, e);
    }

    /**
     * 等待所有任务完成
     */
    private boolean waitForCompletion(List<Future<?>> futures, int totalTasks) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(totalTasks);
        
        // 为每个Future添加完成回调
        for (Future<?> future : futures) {
            DELIVERY_MANUAL_PACK_APPLY_EXECUTOR.submit(() -> {
                try {
                    future.get();
                } catch (Exception e) {
                    // 异常已在handleTaskException中处理
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有任务完成，设置60秒超时
        boolean completed = latch.await(60, TimeUnit.SECONDS);
        if (!completed) {
            log.warn("批量签约超时，已等待60秒，取消未完成的任务");
            cancelUnfinishedTasks(futures);
        }
        
        return !completed;
    }

    /**
     * 取消未完成的任务
     */
    private void cancelUnfinishedTasks(List<Future<?>> futures) {
        int cancelledCount = 0;
        for (Future<?> future : futures) {
            if (!future.isDone() && future.cancel(true)) {
                cancelledCount++;
            }
        }
        log.warn("已取消 {} 个未完成的任务", cancelledCount);
    }

    /**
     * 收集执行结果
     */
    private BatchExecutionResult collectResults(List<Future<?>> futures, int totalTasks, 
                                              boolean timeoutOccurred, long startTime) {
        List<String> failureDetails = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < futures.size(); i++) {
            Future<?> future = futures.get(i);
            try {
                if (future.isDone() && !future.isCancelled()) {
                    future.get(0, TimeUnit.MILLISECONDS); // 立即获取结果
                    successCount++;
                } else if (future.isCancelled()) {
                    failureCount++;
                    failureDetails.add(String.format("批次 %d 被取消", i));
                }
            } catch (ExecutionException e) {
                failureCount++;
                failureDetails.add(String.format("批次 %d 执行异常: %s", i, e.getCause().getMessage()));
                log.error("批次 {} 执行异常", i, e.getCause());
            } catch (TimeoutException | InterruptedException e) {
                // 忽略这些异常，因为我们已经通过latch等待了
            }
        }
        
        long executionTimeMs = System.currentTimeMillis() - startTime;
        return new BatchExecutionResult(totalTasks, successCount, failureCount, failureDetails, timeoutOccurred, executionTimeMs);
    }

    private void applyBatchDeliverySign(HeronContractManualBatchSignParam param) throws WmCustomerException {
        try {
            log.info("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.manualBatchApplySign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.warn("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, warn param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, TException, param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口发起配送合同批量签约系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#applyBatchDeliverySign, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口发起配送合同批量签约系统异常");
        }
    }

    /**
     * 通过售卖网关取消签约任务
     * @param param
     * @throws WmCustomerException
     */
    public void deliveryCancelSignUseNewIface(HeronContractEcontractCancelSignParam param) throws WmCustomerException {
        try {
            log.info("#econtractCancelSign param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.econtractCancelSign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.error("#deliveryCancelSignUseNewIface 使用售卖网关接口取消配送合同签约中任务业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#deliveryCancelSignUseNewIface 使用售卖网关接口取消配送合同签约中任务系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消配送合同签约中任务系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#deliveryCancelSignUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消配送合同签约中任务系统异常");
        }
    }

    /**
     * 通过售卖网关取消待打包签约任务
     * @param param
     * @throws WmCustomerException
     */
    public void deliveryCancelManualSignUseNewIface(HeronContractEcontractCancelManualSignParam param) throws WmCustomerException {
        try {
            log.info("#econtractCancelManualSign param:{}", JSON.toJSONString(param));
            heronContractFlowProcessGatewayThriftService.econtractCancelManualSign(param);
        } catch (HeronContractGatewayThriftException e) {
            log.error("#deliveryCancelManualSignUseNewIface 使用售卖网关接口取消待打包签约任务业务异常 param:{}", JSON.toJSONString(param), e);
            throw new WmCustomerException(CustomerErrorCodeConstants.BIZ_ERROR, e.getMessage());
        } catch (TException ex) {
            log.error("#deliveryCancelManualSignUseNewIface 使用售卖网关接口取消待打包签约任务系统异常 param:{}", JSON.toJSONString(param), ex);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消待打包签约任务系统异常");
        } catch (Exception e) {
            log.error("WmLogisticsGatewayThriftServiceAdapter#deliveryCancelManualSignUseNewIface, error", e);
            throw new WmCustomerException(CustomerErrorCodeConstants.SYSTEM_ERROR, "使用售卖网关接口取消待打包签约任务系统异常");
        }
    }
}
